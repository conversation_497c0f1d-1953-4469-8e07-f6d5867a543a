name: Build Project

on:
  pull_request:
    types: [opened, synchronize, reopened]
  push:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  build:
    name: "Build ${{ matrix.platform }} in ${{ matrix.build_type }}"
    strategy:
      matrix:
        platform: [windows, ubuntu]
        build_type: [Debug, Release]
    env:
      PARALLEL: -j 2
    runs-on: "${{ matrix.platform }}-latest"
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - if: ${{ matrix.platform == 'ubuntu' }}
        name: Install RandR headers
        run: |
          sudo apt-get update
          sudo apt install xorg-dev libglu1-mesa-dev
      - if: ${{ matrix.platform == 'windows' }}
        name: Setup MSVC
        uses: ilammy/msvc-dev-cmd@v1
      - if: ${{ matrix.platform == 'windows' }}
        name: sccache
        uses: hendrikmuhs/ccache-action@v1.2.9
        with:
          key: ${{ github.job }}-${{ matrix.os }}
          variant: sccache
      - if: ${{ matrix.platform != 'windows' }}
        name: ccache
        uses: hendrikmuhs/ccache-action@v1.2.9
        with:
          key: ${{ github.job }}-${{ matrix.os }}
      - if: ${{ matrix.platform == 'windows' }}
        name: Configure and build
        shell: pwsh
        run: |
          cmake -B"build/${{ matrix.platform }}" -G"Ninja" -DVKB_BUILD_TESTS=ON -DVKB_BUILD_SAMPLES=ON -DCMAKE_C_COMPILER_LAUNCHER=C:\\Users\\<USER>\\.cargo\\bin\\sccache -DCMAKE_CXX_COMPILER_LAUNCHER=C:\\Users\\<USER>\\.cargo\\bin\\sccache
          cmake --build "build/${{ matrix.platform }}" --target vulkan_samples --config ${{ matrix.build_type }}  -j 1
      - if: ${{ matrix.platform != 'windows' }}
        name: Configure and build
        run: |
          cmake -B"build/${{ matrix.platform }}" -DVKB_BUILD_TESTS=ON -DVKB_BUILD_SAMPLES=ON -DGLFW_BUILD_WAYLAND=OFF
          cmake --build "build/${{ matrix.platform }}" --target vulkan_samples --config ${{ matrix.build_type }} ${{ env.PARALLEL }}

  build_v2:
    name: "Build ${{ matrix.platform }} in ${{ matrix.build_type }}"
    strategy:
      matrix:
        platform: [windows, ubuntu]
        build_type: [Release]
    env:
      PARALLEL: -j 2
    runs-on: "${{ matrix.platform }}-latest"
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - if: ${{ matrix.platform == 'ubuntu' }}
        name: Install RandR headers
        run: |
          sudo apt-get update
          sudo apt install xorg-dev libglu1-mesa-dev

      - name: Configure
        run: cmake -B"build/${{ matrix.platform }}" -DVKB_BUILD_TESTS=ON -DGLFW_BUILD_WAYLAND=OFF

      - name: "Build Components ${{ matrix.platform }} in ${{ matrix.build_type }}"
        run: cmake --build "build/${{ matrix.platform }}" --target vkb__components --config ${{ matrix.build_type }} ${{ env.PARALLEL }}

  build_macos:
    needs: build
    name: "Build ${{ matrix.platform }} in ${{ matrix.build_type }}"
    strategy:
      # masoc is 10x expensive to run on GitHub machines, so only build simplest variations
      matrix:
        platform: [macos]
        # Release builds are about 3 times faster than debug builds
        build_type: [Release]
    env:
      PARALLEL: -j 2
    runs-on: "${{ matrix.platform }}-latest"
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2.9
        with:
          key: ${{ github.job }}-${{ matrix.os }}
      - name: Configure and build
        run: |
          cmake -B"build/${{ matrix.platform }}" -DVKB_BUILD_TESTS=ON -DVKB_BUILD_SAMPLES=ON -DCMAKE_OSX_SYSROOT=macosx -DCMAKE_OSX_DEPLOYMENT_TARGET=11.0
          cmake --build "build/${{ matrix.platform }}" --target vulkan_samples --config ${{ matrix.build_type }} ${{ env.PARALLEL }}

  build_d2d:
    name: "Build Ubuntu with Direct To Display"
    env:
      PARALLEL: -j 2
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - name: Install RandR headers
        run: |
          sudo apt-get update
          sudo apt install xorg-dev libglu1-mesa-dev
      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ github.job }}-${{ matrix.os }}
      - name: Configure
        run: cmake -B"build/ubuntu-latest-d2d" -DVKB_BUILD_TESTS=ON -DVKB_BUILD_SAMPLES=ON -DVKB_WSI_SELECTION=D2D
      - name: "Build Ubuntu in Release with VKB_WSI_SELECTION=D2D"
        run: cmake --build "build/ubuntu-latest-d2d" --target vulkan_samples --config Release ${{ env.PARALLEL }}

  build_android:
    name: "Build Android in ${{ matrix.build_type }}"
    runs-on: ubuntu-latest
    strategy:
      matrix:
        build_type: [Debug]

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "zulu"
          cache: gradle
      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2.9
        with:
          key: ${{ github.job }}-${{ matrix.os }}

      - name: "Run generate script"
        run: ./scripts/generate.py android

      - name: "Build Android using gradlew."
        working-directory: build/android_gradle
        run: NDK_CCACHE=1 NDK_CCACHE_BIN=ccache ./gradlew app:assemble${{ matrix.build_type }} --info

  build_ios:
    # iOS is 10x expensive to run on GitHub machines, so only run if we know something else fast/simple passed as well
    needs: build
    name: "Build ios in ${{ matrix.build_type }}"
    runs-on: macos-latest
    strategy:
      matrix:
        build_type: [Release]

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"

      - name: ccache
        uses: hendrikmuhs/ccache-action@v1.2.9
        with:
          key: ${{ github.job }}-${{ matrix.os }}

      - name: Load VulkanSDK from cache
        id: VulkanSDK
        uses: actions/cache@v4
        with:
          path: VulkanSDK
          key: vulkan

      - name: retrieve VulkanSDK
        if: steps.VulkanSDK.outputs.cache-hit != 'true'
        run: |
          wget https://sdk.lunarg.com/sdk/download/1.4.304.1/mac/vulkansdk-macos-1.4.304.1.zip
          unzip vulkansdk-macos-1.4.304.1.zip
          sudo InstallVulkan-1.4.304.1.app/Contents/MacOS/InstallVulkan-1.4.304.1 --root ~/VulkanSDK --accept-licenses --default-answer --confirm-command install com.lunarg.vulkan.ios

      - name: build_ios
        run: |
          source ~/VulkanSDK/iOS/setup-env.sh
          cmake -H"." -B"build/ios" -DVKB_BUILD_TESTS=ON -DVKB_BUILD_SAMPLES=ON -G Xcode -DCMAKE_SYSTEM_NAME=iOS -DCMAKE_OSX_SYSROOT=iphoneos -DCMAKE_OSX_DEPLOYMENT_TARGET=13.0 -DCMAKE_XCODE_ATTRIBUTE_CODE_SIGNING_ALLOWED=NO
          cmake --build "build/ios" --target vulkan_samples --config ${{ matrix.build_type }} -- -allowProvisioningUpdates
