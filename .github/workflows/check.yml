name: Quality Checks

on:
  workflow_dispatch:
  pull_request:
    branches: [main]

env:
  TARGET_BRANCH: main

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.ref }}
  cancel-in-progress: true

jobs:
  changed-files:
    container: ghcr.io/khronosgroupactions/clang-tools:15.0.0
    runs-on: ubuntu-latest
    # Map a step output to a job output
    outputs:
      all: ${{ steps.changes.outputs.all}}
    steps:
      # Make sure we have some code to diff.
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Get changed files
        id: changes
        # Set outputs using the command.
        run: |
          git config --global --add safe.directory /__w/Vulkan-Samples/Vulkan-Samples
          git fetch origin $TARGET_BRANCH:$TARGET_BRANCH
          echo all="$(git diff --name-only --diff-filter=ACMRT $TARGET_BRANCH | grep -v -e .github -e third_party -e .in$ | xargs)" >> $GITHUB_OUTPUT

  doxygen:
    name: Doxygen Syntax Check
    container: ghcr.io/khronosgroupactions/doxygen:1.9.5
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: mkdir -p doxygen
      - run: doxygen docs/doxygen/doxyfile
      - run: if [ $(stat -c%s doxygen/warnings.txt) -gt 0 ]; then cat doxygen/warnings.txt; exit 1; fi

  copyright_headers:
    name: Copyright Headers Check
    container: ghcr.io/khronosgroupactions/copyright-check:latest
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: git config --global --add safe.directory /__w/Vulkan-Samples/Vulkan-Samples
      - run: git fetch origin $TARGET_BRANCH:$TARGET_BRANCH
      - run: python3 /usr/local/bin/check_copyright_headers.py $TARGET_BRANCH

  snake_case_check:
    name: Snake Case Check
    container: ghcr.io/khronosgroupactions/snake-case-check:latest
    runs-on: ubuntu-latest
    continue-on-error: true
    steps:
      - uses: actions/checkout@v4
      - run: python3 /usr/local/bin/snake_case_check.py $TARGET_BRANCH > snake-report.txt
      - run: if [ $(grep -c '@@' snake-report.txt) -gt 0 ]; then cat snake-report.txt; exit 1; fi

  clang_format:
    name: Clang Format Check
    container: ghcr.io/khronosgroupactions/clang-tools:15.0.0
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - run: git config --global --add safe.directory /__w/Vulkan-Samples/Vulkan-Samples
      - run: git fetch origin $TARGET_BRANCH:$TARGET_BRANCH
      - name: Run Clang Format diff
        id: clang-diff
        run: |
          echo "changes=$(echo $(git diff -U0 --no-color $TARGET_BRANCH | python3 /usr/share/clang/clang-format-diff.py -p1 -v -sort-include))" >> $GITHUB_OUTPUT
          echo "$(echo $(git diff -U0 --no-color $TARGET_BRANCH | python3 /usr/share/clang/clang-format-diff.py -p1 -v -sort-include))" >> clang-issues.diff
      - name: Count Diff Lines
        continue-on-error: true
        id: count-diff
        run: echo "line-count=$(echo "${{ steps.clang-diff.outputs.changes }}" | grep -c +++)" >> $GITHUB_OUTPUT
      - name: 'Upload Artifact'
        uses: actions/upload-artifact@v4
        with:
          name: clang-issues.diff
          path: clang-issues.diff
          retention-days: 3
      - name: Assert
        run: if test ${{ steps.count-diff.outputs.line-count }} -gt 0; then echo "${{ steps.clang-diff.outputs.changes }}"; exit 1; fi

  clang_tidy:
    name: Clang Tidy Check
    container: ghcr.io/khronosgroupactions/clang-tools:15.0.0
    runs-on: ubuntu-latest
    needs: changed-files
    continue-on-error: true # we currently don't track clang-tidy warnings as errors. in the future this will change
    if: ${{needs.changed-files.outputs.all}}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: "recursive"
      - run: git config --global --add safe.directory /__w/Vulkan-Samples/Vulkan-Samples
      - run: cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON -DGLFW_BUILD_WAYLAND=OFF -Bbuild/clang
      - run: |
          /usr/bin/run-clang-tidy -j $(($(nproc)/2+1)) -p build/clang -header-filter=framework,samples,app -checks=-*,google-*,-google-runtime-references -quiet ${{ needs.changed-files.outputs.all }}

  pre-commit:
    name: Pre-Commit Checks
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v3
      - uses: pre-commit/action@v3.0.1
        continue-on-error: true # we currently don't track pre-commit warnings as errors. in the future this will change. for this step to pass we will need to run `pre-commit run --all-files`

