////
- Copyright (c) 2023-2025, Holochip Inc
- Copyright (c) 2023-2025, <PERSON><PERSON><PERSON>
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
////
* xref:samples/README.adoc[Samples overview]
* xref:samples/vulkan_basics.adoc[Vulkan basics]
* xref:shaders/README.adoc[Shaders]
* xref:framework/README.adoc[Sample framework]
** xref:components/README.adoc[Framework components]
* xref:samples/api/README.adoc[Api usage samples]
** xref:samples/api/compute_nbody/README.adoc[Compute N-body]
*** xref:samples/api/hpp_compute_nbody/README.adoc[Compute N-body (Vulkan-Hpp)]
** xref:samples/api/dynamic_uniform_buffers/README.adoc[Dynamic uniform buffers]
*** xref:samples/api/hpp_dynamic_uniform_buffers/README.adoc[Dynamic Uniform Buffers (Vulkan-Hpp)]
** xref:samples/api/hdr/README.adoc[HDR]
*** xref:samples/api/hpp_hdr/README.adoc[HDR (Vulkan-Hpp)]
** xref:samples/api/hello_triangle/README.adoc[Hello Triangle]
*** xref:samples/api/hpp_hello_triangle/README.adoc[Hello Triangle (Vulkan-Hpp)]
** xref:samples/api/hello_triangle_1_3/README.adoc[Hello Triangle 1.3]
*** xref:samples/api/hpp_hello_triangle_1_3/README.adoc[Hello Triangle 1.3(Vulkan-Hpp)]
** xref:samples/api/instancing/README.adoc[Instancing]
*** xref:samples/api/hpp_instancing/README.adoc[Instancing (Vulkan-Hpp)]
** xref:samples/api/separate_image_sampler/README.adoc[Separate image sampler]
*** xref:samples/api/hpp_separate_image_sampler/README.adoc[Separate image sampler (Vulkan-Hpp)]
** xref:samples/api/terrain_tessellation/README.adoc[Terrain tessellation]
*** xref:samples/api/hpp_terrain_tessellation/README.adoc[Terrain tessellation (Vulkan-Hpp)]
** xref:samples/api/texture_loading/README.adoc[Texture loading]
*** xref:samples/api/hpp_texture_loading/README.adoc[Texture loading (Vulkan-Hpp)]
** xref:samples/api/texture_mipmap_generation/README.adoc[Texture mipmap generation]
*** xref:samples/api/hpp_texture_mipmap_generation/README.adoc[Texture mipmap generation (Vulkan-Hpp)]
** xref:samples/api/timestamp_queries/README.adoc[Timestamp queries]
*** xref:samples/api/hpp_timestamp_queries/README.adoc[Timestamp queries (Vulkan-Hpp)]
** xref:samples/api/oit_linked_lists/README.adoc[OIT linked lists]
*** xref:samples/api/hpp_oit_linked_lists/README.adoc[OIT linked lists (Vulkan-Hpp)]
** xref:samples/api/oit_depth_peeling/README.adoc[OIT depth peeling]
*** xref:samples/api/hpp_oit_depth_peeling/README.adoc[OIT depth peeling (Vulkan-Hpp)]
* xref:samples/extensions/README.adoc[Extension usage samples]
** xref:samples/extensions/buffer_device_address/README.adoc[Buffer device address]
** xref:samples/extensions/calibrated_timestamps/README.adoc[Calibrated timestamps]
** xref:samples/extensions/conditional_rendering/README.adoc[Conditional rendering]
** xref:samples/extensions/conservative_rasterization/README.adoc[Conservative rasterization]
** xref:samples/extensions/debug_utils/README.adoc[Debug utils]
** xref:samples/extensions/descriptor_buffer_basic/README.adoc[Descriptor buffer basic]
** xref:samples/extensions/descriptor_indexing/README.adoc[Descriptor indexing]
** xref:samples/extensions/dynamic_line_rasterization/README.adoc[Dynamic line rasterization]
** xref:samples/extensions/dynamic_primitive_clipping/README.adoc[Dynamic primitive clipping]
** xref:samples/extensions/dynamic_rendering/README.adoc[Dynamic rendering]
** xref:samples/extensions/dynamic_rendering_local_read/README.adoc[Dynamic rendering local read]
** xref:samples/extensions/extended_dynamic_state2/README.adoc[Extended dynamic state2]
** xref:samples/extensions/fragment_shader_barycentric/README.adoc[Fragment shader barycentric]
** xref:samples/extensions/fragment_shading_rate/README.adoc[Fragment shading rate]
** xref:samples/extensions/fragment_shading_rate_dynamic/README.adoc[Fragment shading rate dynamic]
** xref:samples/extensions/full_screen_exclusive/README.adoc[Full screen exclusive]
** xref:samples/extensions/graphics_pipeline_library/README.adoc[Graphics pipeline library]
** xref:samples/extensions/gshader_to_mshader/README.adoc[Geometry shader to mesh shader]
** xref:samples/extensions/host_image_copy/README.adoc[Host image copy]
** xref:samples/extensions/logic_op_dynamic_state/README.adoc[Logic op dynamic state]
** xref:samples/extensions/memory_budget/README.adoc[Memory budget]
** xref:samples/extensions/mesh_shader_culling/README.adoc[Mesh shader culling]
** xref:samples/extensions/mesh_shading/README.adoc[Mesh shading]
*** xref:samples/extensions/hpp_mesh_shading/README.adoc[Mesh shading (Vulkan-Hpp)]
** xref:samples/extensions/open_cl_interop/README.adoc[OpenCL interop]
** xref:samples/extensions/open_cl_interop_arm/README.adoc[OpenCL interop (Arm)]
** xref:samples/extensions/open_gl_interop/README.adoc[OpenGL interop]
** xref:samples/extensions/portability/README.adoc[Portability]
** xref:samples/extensions/push_descriptors/README.adoc[Push descriptors]
*** xref:samples/extensions/hpp_push_descriptors/README.adoc[Push descriptors (Vulkan-Hpp)]
** xref:samples/extensions/ray_tracing_basic/README.adoc[Raytracing basic]
** xref:samples/extensions/ray_tracing_extended/README.adoc[Raytracing extended]
** xref:samples/extensions/ray_queries/README.adoc[Ray queries]
** xref:samples/extensions/ray_tracing_reflection/README.adoc[Ray tracing reflection]
** xref:samples/extensions/ray_tracing_position_fetch/README.adoc[Ray tracing position fetch]
** xref:samples/extensions/shader_object/README.adoc[Shader Object]
** xref:samples/extensions/shader_debugprintf/README.adoc[Shader Debug Printf]
** xref:samples/extensions/sparse_image/README.adoc[Sparse Image]
** xref:samples/extensions/synchronization_2/README.adoc[Synchronization 2]
** xref:samples/extensions/timeline_semaphore/README.adoc[Timeline semaphore]
** xref:samples/extensions/vertex_dynamic_state/README.adoc[Vertex dynamic state]
** xref:samples/extensions/dynamic_multisample_rasterization/README.adoc[Dynamic multisample rasterization]
* xref:samples/performance/README.adoc[Performance samples]
** xref:samples/performance/16bit_arithmetic/README.adoc[16bit arithmetic]
** xref:samples/performance/16bit_storage_input_output/README.adoc[16bit storage input output]
** xref:samples/performance/afbc/README.adoc[AFBC]
** xref:samples/performance/async_compute/README.adoc[Async compute]
** xref:samples/performance/command_buffer_usage/README.adoc[Command buffer usage]
** xref:samples/performance/constant_data/README.adoc[Constant data]
** xref:samples/performance/descriptor_management/README.adoc[Descriptor management]
** xref:samples/performance/image_compression_control/README.adoc[Image compression control]
** xref:samples/performance/layout_transitions/README.adoc[Layout transitions]
** xref:samples/performance/msaa/README.adoc[MSAA]
** xref:samples/performance/multithreading_render_passes/README.adoc[Multithreading render passes]
** xref:samples/performance/multi_draw_indirect/README.adoc[Multi draw indirect]
** xref:samples/performance/pipeline_barriers/README.adoc[Pipeline barriers]
** xref:samples/performance/pipeline_cache/README.adoc[Pipeline cache]
*** xref:samples/performance/hpp_pipeline_cache/README.adoc[Pipeline cache (Vulkan-Hpp)]
** xref:samples/performance/render_passes/README.adoc[Render passes]
** xref:samples/performance/specialization_constants/README.adoc[Specialization constants]
** xref:samples/performance/subpasses/README.adoc[Subpasses]
** xref:samples/performance/surface_rotation/README.adoc[Surface rotation]
** xref:samples/performance/swapchain_images/README.adoc[Swapchain images]
*** xref:samples/performance/hpp_swapchain_images/README.adoc[Swapchain images (Vulkan-Hpp)]
** xref:samples/performance/texture_compression_basisu/README.adoc[Texture compression basisu]
** xref:samples/performance/texture_compression_comparison/README.adoc[Texture compression comparison]
*** xref:samples/performance/hpp_texture_compression_comparison/README.adoc[Texture compression comparison (Vulkan-Hpp)]
** xref:samples/performance/wait_idle/README.adoc[Wait idle]
* xref:samples/tooling/README.adoc[Tooling samples]
** xref:samples/tooling/profiles/README.adoc[Profiles]
* xref:samples/general/README.adoc[General samples]
** xref:samples/general/mobile_nerf/README.adoc[Mobile NeRF]
** xref:samples/general/mobile_nerf_rayquery/README.adoc[Mobile NeRF Ray Query]
* xref:docs/README.adoc[General documentation]
** xref:docs/build.adoc[Build guide]
** xref:docs/memory_limits.adoc[Memory limits]
** xref:docs/misc.adoc[Miscellaneous]
