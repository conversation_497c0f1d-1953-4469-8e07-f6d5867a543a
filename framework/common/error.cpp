/* Copyright (c) 2018-2020, Arm Limited and Contributors
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "error.h"

#include "helpers.h"

namespace vkb
{
VulkanException::VulkanException(const VkResult result, const std::string &msg) :
    result{result},
    std::runtime_error{msg}
{
	error_message = std::string(std::runtime_error::what()) + std::string{" : "} + to_string(result);
}

const char *VulkanException::what() const noexcept
{
	return error_message.c_str();
}
}        // namespace vkb
