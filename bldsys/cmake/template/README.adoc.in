////
- Copyright (c) 2019-2023, The Khronos Group
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
////

// NOTE: Remove the following comment block for the actual readme of your sample -->

////
This is a template for the sample's readme. Every new sample should come with a readme that contains at least a short tutorial that accompanies the code of the example.

Readmes are written in Asciidoc (see https://asciidoc.org/).

You can freely choose how to structure it, but it should always contain an overview and conclusion paragraph.

The readme can (and most often should) show code from along with an explanation. Code in asciidoc can be rendered with syntax highlighting using the following syntax:

[,cpp]
----
void main() {
    std::cout << "Hello World";
}
----

or

[,glsl]
----
void main() {
    gl_color = vec4(1.0f);
}
----

Ideally it also contains an image of how the sample is supposed to look.

For an example you can take a look at the readme's of existing samples, e.g. https://raw.githubusercontent.com/KhronosGroup/Vulkan-Samples/main/samples/extensions/descriptor_indexing/README.adoc
////

= Sample name

////
The following block adds linkage to this repo in the Vulkan docs site project. It's only visible if the file is viewed via the Antora framework.
////

ifdef::site-gen-antora[]
TIP: The source for this sample can be found in the https://github.com/KhronosGroup/Vulkan-Samples/tree/main/samples/PUT_SAMPLE_PATH_HERE[Khronos Vulkan samples github repository].
endif::[]


== Overview

////
This chapter should contain an overview of what this sample is trying to achieve. If extensions are used, this chapter should also list those.

The following chapters get into the details on how the sample is working, what features are used, etc. 

The chapter itself can be structured by using sub paragraphs, e.g.:

# Feature description

# Enabling extensions

# etc.

////

== Conclusion

////
The tutorial should end with a conclusion chapter that recaps the tutorial and (if applicable) talks about pros and cons of the features demonstrated in this sample
////

////
NOTE: Please also add a link to the new samples' README.adoc to the file located in ./antora/modules/ROOT/nav.adoc
THis is necessary to have the sample show up on the build for the docs site under https://docs.vulkan.org
////