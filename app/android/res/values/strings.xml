<?xml version="1.0" encoding="utf-8"?>
<!--
- Copyright (c) 2019-2023, Arm Limited and Contributors
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
-->
<resources>
	<string name="app_name">Vulkan Samples</string>
	<string name="channel_name">vkb_channel</string>
	<string name="channel_desc">vkb_channel is a channel for the vulkan best practice application notifications</string>
	<string name="native_lib_name">vulkan_samples</string>
	<string name="permissions_text">Some permissions were not granted</string>
	<string name="permissions_button">Grant Permissions</string>
	<string name="menu_run_samples_title">Run All Samples</string>
	<string name="open_file_with">Open File With</string>
	<string name="menu_benchmark_mode_title">Benchmark Mode</string>
	<string name="menu_headless_title">Headless</string>
	<string name="filter_button_text">Filter</string>
</resources>
