<?xml version="1.0" encoding="utf-8"?>
<!--
- Copyright (c) 2019-2020, Arm Limited and Contributors
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
-->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/title_text"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:paddingLeft="10dip"
        android:paddingTop="10dip"
        android:textSize="16dip"
        android:textStyle="bold"
        tools:text="A Sample Title">
    </TextView>

    <TextView
        android:id="@+id/description_text"
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_below="@id/title_text"
        android:paddingLeft="10dip"
        android:paddingBottom="10dip"
        android:textSize="14dip"
        android:textStyle="italic"
        tools:text="A Sample Description">
    </TextView>

    <TextView
        android:id="@+id/vendor_text"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentRight="true"
        android:gravity="right"
        android:paddingTop="4dip"
        android:paddingRight="10dip"
        android:textColor="@color/colorPrimary"
        android:textSize="12dip"
        android:textStyle="italic"
        tools:text="Tag, Tag">
    </TextView>

</RelativeLayout>
