<?xml version="1.0" encoding="utf-8"?>
<!--
- Copyright (c) 2019, Arm Limited and Contributors
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
-->
<menu xmlns:android="http://schemas.android.com/apk/res/android">

    <item
        android:id="@+id/filter_button"
        android:title="@string/filter_button_text" />
    <item
        android:id="@+id/menu_benchmark_mode"
        android:checkable="true"
        android:title="@string/menu_benchmark_mode_title"/>
    <item
        android:id="@+id/menu_headless"
        android:checkable="true"
        android:title="@string/menu_headless_title" />
    <item
        android:id="@+id/menu_run_samples"
        android:title="@string/menu_run_samples_title"/>

</menu>
