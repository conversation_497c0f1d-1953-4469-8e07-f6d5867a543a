<?xml version="1.0" encoding="utf-8"?>
<!--
- Copyright (c) 2019-2024, Arm Limited and Contributors
-
- SPDX-License-Identifier: Apache-2.0
-
- Licensed under the Apache License, Version 2.0 the "License";
- you may not use this file except in compliance with the License.
- You may obtain a copy of the License at
-
-     http://www.apache.org/licenses/LICENSE-2.0
-
- Unless required by applicable law or agreed to in writing, software
- distributed under the License is distributed on an "AS IS" BASIS,
- WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
- See the License for the specific language governing permissions and
- limitations under the License.
-
-->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	android:versionCode="1"
	android:versionName="1.0">

	<application android:label="@string/app_name"
		android:debuggable="true"
		android:icon="@mipmap/ic_launcher"
		android:roundIcon="@mipmap/ic_launcher_round"
		android:theme="@style/AppTheme"
		tools:ignore="GoogleAppIndexingWarning,HardcodedDebugMode">

		<activity android:name="com.khronos.vulkan_samples.SampleLauncherActivity"
			android:exported="true">
			<intent-filter>
				<action android:name="android.intent.action.MAIN" />
				<category android:name="android.intent.category.LAUNCHER" />
			</intent-filter>
		</activity>

		<activity android:name="com.khronos.vulkan_samples.NativeSampleActivity"
			android:configChanges="orientation|keyboardHidden|screenSize"
			android:screenOrientation="fullSensor">
			<meta-data android:name="android.app.lib_name"
				android:value="@string/native_lib_name" />
		</activity>

		<provider
			android:authorities="${applicationId}.provider"
			android:name="androidx.core.content.FileProvider"
			android:exported="false"
			android:grantUriPermissions="true">
			<meta-data
				android:name="android.support.FILE_PROVIDER_PATHS"
				android:resource="@xml/provider_paths"/>
		</provider>

	</application>

</manifest>
