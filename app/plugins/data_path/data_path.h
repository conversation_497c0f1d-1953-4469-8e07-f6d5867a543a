/* Copyright (c) 2022-2025, Arm Limited and Contributors
 * Copyright (c) 2025, NVIDIA CORPORATION. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include "platform/plugins/plugin_base.h"

namespace plugins
{
using DataPathTags = vkb::PluginBase<vkb::tags::Passive>;

/**
 * @brief Data path override
 *
 * Controls the root path used to find data files
 *
 * Usage: vulkan_sample sample afbc --data-path <folder>
 *
 */
class DataPath : public DataPathTags
{
  public:
	DataPath();

	virtual ~DataPath() = default;

	bool handle_option(std::deque<std::string> &arguments) override;
};
}        // namespace plugins