/* Copyright (c) 2020-2025, Arm Limited and Contributors
 * Copyright (c) 2025, NVIDIA CORPORATION. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "force_close.h"

#include <iostream>

namespace plugins
{
ForceClose::ForceClose() :
    ForceCloseTags("Force Close",
                   "Force the application to close if it has been halted before exiting",
                   {},
                   {},
                   {{"force-close", "Force the close of the application if halted before exiting"}})
{
}

bool ForceClose::handle_option(std::deque<std::string> &arguments)
{
	assert(!arguments.empty() && (arguments[0].substr(0, 2) == "--"));
	std::string option = arguments[0].substr(2);
	if (option == "force-close")
	{
		arguments.pop_front();
		return true;
	}
	return false;
}
}        // namespace plugins