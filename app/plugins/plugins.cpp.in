/* Copyright (c) 2020-2021, Arm Limited and Contributors
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Generated file by CMake. Don't edit.

#include "plugins.h"

#include <memory>

@PLUGIN_INCLUDE_FILES@

namespace plugins
{

#define ADD_PLUGIN(name) \
	plugins.emplace_back(std::make_unique<name>())

std::vector<vkb::Plugin *> get_all()
{
	static bool once = true;
	static std::vector<std::unique_ptr<vkb::Plugin>> plugins;

	if (once) {
		once = false;
@INIT_PLUGINS@
	}

	std::vector<vkb::Plugin *> ptrs;
	ptrs.reserve(plugins.size());

	for (auto &plugin : plugins)
	{
		ptrs.push_back(plugin.get());
	}

	return ptrs;
}
}        // namespace plugins
