/* Copyright (c) 2020-2025, Arm Limited and Contributors
 * Copyright (c) 2021-2025, <PERSON><PERSON><PERSON>
 * Copyright (c) 2025, NVIDIA CORPORATION. All rights reserved.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#pragma once

#include "platform/plugins/plugin_base.h"

namespace plugins
{
using FileLoggerTags = vkb::PluginBase<vkb::tags::Passive>;

/**
 * @brief File Logger
 * 
 * Enables writing log messages to a file
 * 
 * Usage: vulkan_sample --log-file filename.txt
 * 
 */
class FileLogger : public FileLoggerTags
{
  public:
	FileLogger();

	virtual ~FileLogger() = default;

	bool handle_option(std::deque<std::string> &arguments) override;
};
}        // namespace plugins
