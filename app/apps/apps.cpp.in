/* Copyright (c) 2020-2024, Arm Limited and Contributors
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 the "License";
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Generated file by CMake. Don't edit.
#include "apps.h"

@SAMPLE_INCLUDE_FILES@

namespace apps
{
std::vector<AppInfo> apps = {
@APP_INFO_LIST@
};

std::vector<SampleInfo> samples = {
@SAMPLE_INFO_LIST@
};

AppInfo *get_app(const std::string &id)
{
	for (auto &app : apps)
	{
		if (app.id == id)
		{
			return &app;
		}
	}

	return nullptr;
}

std::vector<AppInfo *> get_apps()
{
	std::vector<AppInfo *> app_ptrs;

	for (auto &app : apps)
	{
		app_ptrs.push_back(&app);
	}

	return app_ptrs;
}

std::vector<AppInfo *> get_samples(const std::vector<std::string> &categories, const std::vector<std::string> &tags)
{
	std::vector<AppInfo *> app_ptrs;

	for (auto &app : samples)
	{
		app_ptrs.push_back(&app);
	}

	// No filters
	if (categories.empty() && tags.empty())
	{
		return app_ptrs;
	}

	std::vector<AppInfo *> filtered;

	for (auto app : app_ptrs)
	{
		auto *sample = reinterpret_cast<SampleInfo *>(app);

		bool selected = false;

		// Sample is in one of the desired categories
		for (auto &category : categories)
		{
			if (category == sample->category)
			{
				selected = true;
				break;
			}
		}

		// Sample has one or more of the desired tags
		for (auto &tag : tags)
		{
			for (auto &s_tag : sample->tags)
			{
				if (s_tag == tag)
				{
					selected = true;
					break;
				}
			}
		}

		if (selected)
		{
			filtered.push_back(app);
		}
	}

	return filtered;
}

SampleInfo * get_sample(const std::string &id) {
	for (auto& sample : samples) {
		if (sample.id == id) {
			return &sample;
		}
	}
	return nullptr;
}

}        // namespace apps
